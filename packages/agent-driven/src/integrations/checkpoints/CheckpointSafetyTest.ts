import * as path from 'path';
import * as fs from 'fs/promises';
import { getShadowGitPath, hashWorkingDir } from './CheckpointUtils';
import { GitOperations } from './CheckpointGitOperations';

/**
 * 测试checkpoint安全性的工具类
 * 用于验证shadow git操作不会影响用户的主仓库
 */
export class CheckpointSafetyTest {
  /**
   * 测试shadow git路径生成是否安全
   * @param workspaceDir 用户工作区目录
   * @param globalStoragePath VS Code全局存储路径
   * @param taskId 任务ID
   */
  static async testShadowGitPathSafety(
    workspaceDir: string,
    globalStoragePath: string,
    taskId: string
  ): Promise<{ safe: boolean; reason?: string }> {
    try {
      const cwdHash = hashWorkingDir(workspaceDir);
      const shadowGitPath = await getShadowGitPath(globalStoragePath, taskId, cwdHash);
      
      // 检查shadow git路径是否在用户工作区外
      const userMainGitPath = path.join(workspaceDir, '.git');
      if (shadowGitPath === userMainGitPath) {
        return { safe: false, reason: 'Shadow git路径与用户主git路径相同' };
      }
      
      const shadowGitDir = path.dirname(shadowGitPath);
      const relativePath = path.relative(workspaceDir, shadowGitDir);
      if (!relativePath.startsWith('..')) {
        return { safe: false, reason: 'Shadow git位于用户工作区内' };
      }
      
      return { safe: true };
    } catch (error) {
      return { safe: false, reason: `路径生成失败: ${error instanceof Error ? error.message : String(error)}` };
    }
  }

  /**
   * 测试git操作配置是否安全
   * @param shadowGitPath shadow git路径
   * @param workspaceDir 用户工作区目录
   */
  static async testGitOperationSafety(
    shadowGitPath: string,
    workspaceDir: string
  ): Promise<{ safe: boolean; reason?: string }> {
    try {
      const gitOps = new GitOperations(workspaceDir);
      
      // 测试安全验证
      await gitOps.verifyShadowGitSafety(shadowGitPath, workspaceDir);
      
      return { safe: true };
    } catch (error) {
      return { safe: false, reason: error instanceof Error ? error.message : String(error) };
    }
  }

  /**
   * 检查是否存在可能导致用户主仓库被影响的配置
   * @param workspaceDir 用户工作区目录
   */
  static async checkForDangerousConfigurations(workspaceDir: string): Promise<{
    safe: boolean;
    issues: string[];
  }> {
    const issues: string[] = [];
    
    try {
      // 检查用户主仓库是否存在
      const userMainGitPath = path.join(workspaceDir, '.git');
      try {
        const stats = await fs.stat(userMainGitPath);
        if (stats.isDirectory()) {
          // 检查是否有JoyCoder的标记（这表明可能被错误配置）
          const gitConfigPath = path.join(userMainGitPath, 'config');
          try {
            const configContent = await fs.readFile(gitConfigPath, 'utf-8');
            if (configContent.includes('joycoder.shadowGit')) {
              issues.push('用户主git仓库包含JoyCoder shadow git标记');
            }
            if (configContent.includes('JoyCode Checkpoint')) {
              issues.push('用户主git仓库配置了JoyCode Checkpoint用户');
            }
          } catch (error) {
            // 无法读取配置文件，可能是权限问题，跳过
          }
        }
      } catch (error) {
        // 用户主git不存在，这是正常的
      }
      
      return { safe: issues.length === 0, issues };
    } catch (error) {
      issues.push(`检查配置时出错: ${error instanceof Error ? error.message : String(error)}`);
      return { safe: false, issues };
    }
  }

  /**
   * 运行完整的安全性测试
   * @param workspaceDir 用户工作区目录
   * @param globalStoragePath VS Code全局存储路径
   * @param taskId 任务ID
   */
  static async runFullSafetyTest(
    workspaceDir: string,
    globalStoragePath: string,
    taskId: string
  ): Promise<{
    safe: boolean;
    results: {
      pathSafety: { safe: boolean; reason?: string };
      operationSafety: { safe: boolean; reason?: string };
      configurationCheck: { safe: boolean; issues: string[] };
    };
  }> {
    const pathSafety = await this.testShadowGitPathSafety(workspaceDir, globalStoragePath, taskId);
    
    let operationSafety = { safe: true };
    if (pathSafety.safe) {
      try {
        const cwdHash = hashWorkingDir(workspaceDir);
        const shadowGitPath = await getShadowGitPath(globalStoragePath, taskId, cwdHash);
        operationSafety = await this.testGitOperationSafety(shadowGitPath, workspaceDir);
      } catch (error) {
        operationSafety = { safe: false, reason: `操作测试失败: ${error instanceof Error ? error.message : String(error)}` };
      }
    } else {
      operationSafety = { safe: false, reason: '路径不安全，跳过操作测试' };
    }
    
    const configurationCheck = await this.checkForDangerousConfigurations(workspaceDir);
    
    const overall = pathSafety.safe && operationSafety.safe && configurationCheck.safe;
    
    return {
      safe: overall,
      results: {
        pathSafety,
        operationSafety,
        configurationCheck,
      },
    };
  }
}
